import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { AuthorizationQueue } from '@prisma/client';
import { AuthTokenService } from 'src/auth-token/auth-token.service';
import { ACTIONS_CONSTANT } from 'src/authorization-queue/constants/entity.constants';
import { MODULE_CONSTANT } from 'src/authorization-queue/constants/module.constant';
import { AuthorizationRequestMaker } from 'src/common/maker/authorization-request.maker';
import { DatabaseService } from 'src/database/database.service';
import { BulkCreateSalaryPackageDto } from './dto/bulk-create-salary-package.dto';
import { CreateSalaryPackageDto } from './dto/create-salary-package.dto';

@Injectable()
export class SalaryPackageService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly authTokenService: AuthTokenService,
    private readonly authorizationRequestMaker: AuthorizationRequestMaker,
  ) {}

  async findSalaryPackage({
    identifier,
    token,
  }: {
    identifier: string;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    const department = this.databaseService.salaryPackage.findFirst({
      where: {
        OR: [
          { id: identifier },
          {
            name: `${decodedToken.companyId}|${identifier}`,
            companyId: decodedToken.companyId,
          },
        ],
      },
    });

    return department;
  }
  // Method to find a role by ID or Name
  async getSalaryPackages(token: string) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      const { companyId } = decodedToken;
      const salaryPackages = await this.databaseService.salaryPackage.findMany({
        where: {
          companyId,
        },
      });

      return salaryPackages.map((salaryPackage) => ({
        ...salaryPackage,
        name: salaryPackage.name.split('|')[1],
      }));
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  // Method to create a new role
  async acceptSalaryPackageAction({
    queue,
    approvedBy,
  }: {
    queue: AuthorizationQueue;
    approvedBy: string;
  }) {
    const { requestedBy, action, companyId } = queue;
    switch (action) {
      case 'CREATE': {
        const {
          name,
          description,
          baseSalary,
          currency,
          nhfRate,
          pensionRate,
          taxAmount,
          annualGrossSalary,
          apprenticeAllowance,
          childEducationSubsidy,
          domesticStaffAllowance,
          fuelSubsidy,
          furnitureAllowance,
          hazardOrEntertainmentAllowance,
          housingAllowance,
          levelProficiencyAllowance,
          monthlyGrossSalary,
          responsibilityAllowance,
          selfMaintenanceAllowance,
          transportAllowance,
          utilityAllowance,
          gradeLevelId,
        } = JSON.parse(queue.data) as CreateSalaryPackageDto;

        // Validate gradeLevelId if provided
        if (gradeLevelId) {
          const gradeLevel = await this.databaseService.gradeLevel.findUnique({
            where: {
              id: gradeLevelId,
              companyId,
            },
          });

          if (!gradeLevel) {
            throw new NotFoundException('Grade level not found');
          }
        }

        // Check if the group already exists by name
        const salaryPackageExist =
          await this.databaseService.salaryPackage.findUnique({
            where: {
              name_companyId: {
                name: `${companyId}|${name}`,
                companyId,
              },
            },
          });

        if (salaryPackageExist) {
          throw new BadRequestException('Salary package already exists');
        }

        await this.databaseService.salaryPackage.create({
          data: {
            name: `${companyId}|${name}`,
            description: description || name,
            companyId,
            baseSalary,
            currency,
            nhfRate,
            pensionRate,
            taxAmount,
            createdBy: requestedBy,
            approvedBy,
            annualGrossSalary,
            apprenticeAllowance,
            childEducationSubsidy,
            domesticStaffAllowance,
            fuelSubsidy,
            furnitureAllowance,
            hazardOrEntertainmentAllowance,
            housingAllowance,
            levelProficiencyAllowance,
            monthlyGrossSalary,
            responsibilityAllowance,
            selfMaintenanceAllowance,
            transportAllowance,
            utilityAllowance,
            gradeLevelId,
          },
        });

        return true;
      }

      case 'CREATE_BULK': {
        const salaryPackageData = JSON.parse(
          queue.data,
        ) as CreateSalaryPackageDto[];

        // Validate all gradeLevelIds if provided
        for (const packageData of salaryPackageData) {
          if (packageData.gradeLevelId) {
            const gradeLevel = await this.databaseService.gradeLevel.findUnique(
              {
                where: {
                  id: packageData.gradeLevelId,
                  companyId,
                },
              },
            );

            if (!gradeLevel) {
              throw new NotFoundException(
                `Grade level not found for salary package: ${packageData.name}`,
              );
            }
          }
        }

        // Create salary packages
        const salaryPackages = salaryPackageData.map((packageData) => ({
          name: `${companyId}|${packageData.name}`,
          description: packageData.description || packageData.name,
          companyId,
          baseSalary: packageData.baseSalary,
          currency: packageData.currency,
          nhfRate: packageData.nhfRate,
          pensionRate: packageData.pensionRate,
          taxAmount: packageData.taxAmount,
          createdBy: requestedBy,
          approvedBy,
          annualGrossSalary: packageData.annualGrossSalary,
          apprenticeAllowance: packageData.apprenticeAllowance,
          childEducationSubsidy: packageData.childEducationSubsidy,
          domesticStaffAllowance: packageData.domesticStaffAllowance,
          fuelSubsidy: packageData.fuelSubsidy,
          furnitureAllowance: packageData.furnitureAllowance,
          hazardOrEntertainmentAllowance:
            packageData.hazardOrEntertainmentAllowance,
          housingAllowance: packageData.housingAllowance,
          levelProficiencyAllowance: packageData.levelProficiencyAllowance,
          monthlyGrossSalary: packageData.monthlyGrossSalary,
          responsibilityAllowance: packageData.responsibilityAllowance,
          selfMaintenanceAllowance: packageData.selfMaintenanceAllowance,
          transportAllowance: packageData.transportAllowance,
          utilityAllowance: packageData.utilityAllowance,
          gradeLevelId: packageData.gradeLevelId,
        }));

        await this.databaseService.salaryPackage.createMany({
          data: salaryPackages,
        });

        return true;
      }

      default:
        return false;
    }
  }

  async createSalaryPackage({
    payload,
    token,
  }: {
    payload: CreateSalaryPackageDto;
    token: string;
  }) {
    const decodedToken = await this.authTokenService.decodeToken(token);

    try {
      return await this.authorizationRequestMaker.queueRequest({
        payload,
        action: ACTIONS_CONSTANT.create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.SALARY_PACKAGE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.log(error);

      throw new Error('Something went wrong. Try again!');
    }
  }

  async createBulkSalaryPackage(payload: {
    data: BulkCreateSalaryPackageDto;
    token?: string;
  }) {
    try {
      const decodedToken = await this.authTokenService.decodeToken(
        payload.token,
      );

      return await this.authorizationRequestMaker.queueRequest({
        payload: payload.data.salaryPackages,
        action: ACTIONS_CONSTANT.bulk_create,
        companyId: decodedToken.companyId,
        module: MODULE_CONSTANT.SALARY_PACKAGE,
        requestedBy: decodedToken.name || decodedToken.email,
      });
    } catch (error) {
      console.error(error);
      throw error;
    }
  }
}
